import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import viteStyleLint from "vite-plugin-stylelint";
import { fileURLToPath, URL } from "node:url";
import vue from "@vitejs/plugin-vue";
import { defineConfig, UserConfig, UserConfigExport } from "vite";
import Icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import Inspect from "vite-plugin-inspect";

// VSCode 扩展专用配置
export default (): UserConfigExport => {
  return defineConfig({
    plugins: [
      AutoImport({
        imports: ["vue", "vue-router", "pinia"],
        dts: "src/auto-import.d.ts",
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass"
          }),
          IconsResolver({
            prefix: "Icon"
          })
        ]
      }),
      Components({
        include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
        resolvers: [
          IconsResolver({
            enabledCollections: ["ep"]
          }),
          ElementPlusResolver({
            importStyle: "sass"
          })
        ],
        dts: "src/components.d.ts"
      }),
      Icons({
        autoInstall: true
      }),
      viteStyleLint(),
      vue(),
      Inspect()
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url))
      },
      extensions: [".mjs", ".js", ".mts", ".ts", ".jsx", ".tsx", ".json"]
    },
    // VSCode 扩展不需要 base 路径
    base: "./",
    // 针对 VSCode webview 的构建优化
    build: {
      // 输出目录
      outDir: "dist",
      assetsDir: "static",
      // 单文件构建，避免代码分割
      rollupOptions: {
        input: {
          main: fileURLToPath(new URL("./src/main-vscode.ts", import.meta.url))
        },
        output: {
          // 禁用代码分割，确保在 webview 中能正常工作
          manualChunks: undefined,
          entryFileNames: 'index.[hash].js',
          chunkFileNames: 'chunk.[hash].js',
          assetFileNames: (assetInfo) => {
            if (assetInfo.name?.endsWith('.css')) {
              return 'index.[hash].css';
            }
            return 'assets/[name].[hash].[ext]';
          }
        }
      },
      // 生成 source map 用于调试
      sourcemap: true,
      // 压缩配置
      minify: "terser",
      terserOptions: {
        compress: {
          // 保留 console 语句用于调试
          drop_console: false,
          drop_debugger: false
        }
      },
      // 目标环境
      target: ["chrome100", "safari15"],
      // 库模式配置
      lib: false
    },
    // CSS 处理配置
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use '@/assets/scss/element-variables.scss' as *;@use '@/assets/scss/global.scss' as *;@use '@/assets/iconfont/iconfont.scss' as *;`
        }
      },
      devSourcemap: true
    },
    // 开发服务器配置（用于调试扩展）
    server: {
      host: "127.0.0.1",
      port: 5174,
      strictPort: true,
      cors: true,
      headers: {
        "Access-Control-Allow-Origin": "*"
      }
    },
    // 优化配置
    optimizeDeps: {
      include: [
        "vue",
        "vue-router",
        "pinia",
        "element-plus",
        "axios",
        "lodash"
      ]
    }
  });
};
