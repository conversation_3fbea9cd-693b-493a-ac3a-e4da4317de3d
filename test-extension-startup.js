#!/usr/bin/env node

/**
 * 测试 VSCode 扩展启动脚本
 * 验证扩展的基本配置和文件是否正确
 */

import fs from 'fs';
import path from 'path';

console.log('🚀 开始测试 VSCode 扩展启动...\n');

// 测试函数
function test(name, condition, errorMsg = '') {
    const status = condition ? '✅' : '❌';
    console.log(`${status} ${name}`);
    if (!condition && errorMsg) {
        console.log(`   错误: ${errorMsg}`);
    }
    return condition;
}

let allTestsPassed = true;

// 1. 检查基本文件结构
console.log('📁 检查文件结构...');
const requiredFiles = [
    'extension/package.json',
    'extension/out/extension.js',
    '.vscode/launch.json',
    '.vscode/tasks.json'
];

requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    allTestsPassed = test(`${file} 存在`, exists) && allTestsPassed;
});

// 2. 检查扩展配置
console.log('\n⚙️ 检查扩展配置...');
try {
    const packageJson = JSON.parse(fs.readFileSync('extension/package.json', 'utf8'));
    
    allTestsPassed = test('扩展名称正确', packageJson.name === 'sy-gallery-vscode') && allTestsPassed;
    allTestsPassed = test('扩展有激活事件', packageJson.activationEvents && packageJson.activationEvents.length > 0) && allTestsPassed;
    allTestsPassed = test('扩展有命令定义', packageJson.contributes && packageJson.contributes.commands && packageJson.contributes.commands.length > 0) && allTestsPassed;
    allTestsPassed = test('扩展入口文件正确', packageJson.main === './out/extension.js') && allTestsPassed;
    
    // 检查命令
    const commands = packageJson.contributes.commands;
    const hasOpenCommand = commands.some(cmd => cmd.command === 'sy-gallery.open');
    allTestsPassed = test('包含打开画廊命令', hasOpenCommand) && allTestsPassed;
    
} catch (error) {
    allTestsPassed = test('扩展package.json格式正确', false, error.message) && allTestsPassed;
}

// 3. 检查构建产物
console.log('\n🔨 检查构建产物...');
try {
    const extensionJs = fs.readFileSync('extension/out/extension.js', 'utf8');
    allTestsPassed = test('扩展JS文件包含activate函数', extensionJs.includes('function activate')) && allTestsPassed;
    allTestsPassed = test('扩展JS文件包含命令注册', extensionJs.includes('sy-gallery.open')) && allTestsPassed;
    
    // 检查Vue应用构建产物
    const distFiles = fs.readdirSync('dist');
    const hasMainJs = distFiles.some(file => file.startsWith('index.') && file.endsWith('.js'));
    const hasMainCss = distFiles.some(file => file.startsWith('index.') && file.endsWith('.css'));
    
    allTestsPassed = test('Vue应用JS文件存在', hasMainJs) && allTestsPassed;
    allTestsPassed = test('Vue应用CSS文件存在', hasMainCss) && allTestsPassed;
    
} catch (error) {
    allTestsPassed = test('构建产物检查', false, error.message) && allTestsPassed;
}

// 4. 检查VSCode配置
console.log('\n🔧 检查VSCode配置...');
try {
    const launchConfigRaw = fs.readFileSync('.vscode/launch.json', 'utf8');
    const hasExtensionConfig = launchConfigRaw.includes('运行 VSCode 扩展') &&
                              launchConfigRaw.includes('extensionHost');
    allTestsPassed = test('VSCode调试配置正确', hasExtensionConfig) && allTestsPassed;

} catch (error) {
    allTestsPassed = test('VSCode配置检查', false, error.message) && allTestsPassed;
}

// 5. 检查依赖
console.log('\n📦 检查依赖...');
try {
    const nodeModulesExists = fs.existsSync('extension/node_modules');
    allTestsPassed = test('扩展依赖已安装', nodeModulesExists) && allTestsPassed;
    
    if (nodeModulesExists) {
        const vscodeTypesExists = fs.existsSync('extension/node_modules/@types/vscode');
        allTestsPassed = test('VSCode类型定义存在', vscodeTypesExists) && allTestsPassed;
    }
    
} catch (error) {
    allTestsPassed = test('依赖检查', false, error.message) && allTestsPassed;
}

// 总结
console.log('\n📊 测试总结:');
if (allTestsPassed) {
    console.log('🎉 所有测试通过！扩展应该可以正常运行。');
    console.log('\n🚀 启动扩展的步骤:');
    console.log('1. 在VSCode中打开此项目');
    console.log('2. 按 F5 或选择"运行 VSCode 扩展"调试配置');
    console.log('3. 在新窗口中按 Cmd+Shift+P (macOS) 或 Ctrl+Shift+P (Windows/Linux)');
    console.log('4. 输入"打开新氧画廊"命令');
    process.exit(0);
} else {
    console.log('❌ 部分测试失败，请检查上述错误并修复。');
    process.exit(1);
}
