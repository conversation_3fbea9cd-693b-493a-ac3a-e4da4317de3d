#!/bin/bash

echo "🚀 开始构建 VSCode 版本..."

# 删除旧的构建目录
if [ -d "dist" ]; then
    echo "🗑️  删除旧的 dist 目录..."
    rm -rf dist
fi

# 运行 TypeScript 检查
echo "🔍 运行 TypeScript 检查..."
npx vue-tsc --noEmit
if [ $? -ne 0 ]; then
    echo "❌ TypeScript 检查失败"
    exit 1
fi

# 运行 Vite 构建
echo "📦 运行 Vite 构建..."
npx vite build --config vite.config.vscode.ts
if [ $? -ne 0 ]; then
    echo "❌ Vite 构建失败"
    exit 1
fi

# 检查构建结果
if [ -d "dist" ]; then
    echo "✅ 构建成功！"
    echo "📁 构建文件："
    ls -la dist/
else
    echo "❌ 构建失败：dist 目录不存在"
    exit 1
fi

echo "🎉 VSCode 版本构建完成！"
