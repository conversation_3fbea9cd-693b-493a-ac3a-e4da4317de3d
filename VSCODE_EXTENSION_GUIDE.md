# 新氧画廊 VSCode 扩展 - 快速启动指南

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
```bash
./start-vscode-extension.sh
```

### 方法二：手动构建
```bash
# 构建 Vue 应用
npm run build:vscode

# 编译扩展代码
npm run extension:compile
```

## 📋 运行扩展

1. **在 VSCode 中打开项目**
   ```bash
   code .
   ```

2. **启动扩展调试**
   - 按 `F5` 键
   - 或者点击 "运行和调试" → "运行 VSCode 扩展"

3. **在扩展开发宿主窗口中使用**
   - 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows/Linux)
   - 输入 "打开新氧画廊"
   - 或在资源管理器中右键选择 "在新氧画廊中打开"

## 🔧 开发模式

### 并发开发模式（推荐）
```bash
npm run vscode:dev
```
这会同时启动：
- Vue 应用开发服务器（支持热重载）
- 扩展 TypeScript 监视编译

### 分别启动
```bash
# 启动 Vue 应用开发服务器
npm run dev:vscode

# 在另一个终端启动扩展监视编译
npm run extension:watch
```

## 📁 项目结构

```
sy-gallery/
├── extension/                 # VSCode 扩展代码
│   ├── src/extension.ts      # 扩展入口文件
│   ├── package.json          # 扩展配置
│   └── out/                  # 编译输出
├── dist-vscode/              # Vue 应用构建输出
├── src/                      # Vue 应用源码
│   ├── main-vscode.ts        # VSCode 版本入口
│   └── utils/vscode-adapter.ts # VSCode 适配器
├── .vscode/                  # VSCode 配置
│   ├── launch.json           # 调试配置
│   └── tasks.json            # 任务配置
└── vite.config.vscode.ts     # VSCode 版本构建配置
```

## 🛠️ 常用命令

| 命令 | 说明 |
|------|------|
| `npm run build:vscode` | 构建 Vue 应用（VSCode 版本） |
| `npm run dev:vscode` | 启动 Vue 应用开发服务器 |
| `npm run extension:compile` | 编译扩展 TypeScript 代码 |
| `npm run extension:watch` | 监视编译扩展代码 |
| `npm run vscode:build` | 完整构建（Vue + 扩展） |
| `npm run vscode:dev` | 并发开发模式 |

## 🐛 调试技巧

### 1. 扩展调试
- 在 `extension/src/extension.ts` 中设置断点
- 使用 `console.log()` 输出到 VSCode 开发者控制台

### 2. Vue 应用调试
- 在 VSCode 扩展宿主窗口中按 `Cmd+Option+I` (macOS) 打开开发者工具
- 使用 `vscodeAdapter.showInfo()` 显示信息

### 3. 查看日志
- VSCode 开发者控制台：`Help` → `Toggle Developer Tools`
- 扩展输出面板：`View` → `Output` → 选择扩展

## ⚠️ 注意事项

1. **热重载限制**
   - VSCode 扩展不支持热重载
   - 修改扩展代码后需要重新启动扩展宿主窗口（`Cmd+R` 或 `Ctrl+R`）
   - Vue 应用支持热重载

2. **资源路径**
   - 所有资源必须使用 `webview.asWebviewUri()` 转换
   - 不能直接使用相对路径

3. **安全限制**
   - Webview 有严格的 CSP（内容安全策略）
   - 不能执行内联脚本

## 🔍 故障排除

### 扩展无法激活
```bash
# 检查编译输出
ls -la extension/out/

# 重新编译
npm run extension:compile
```

### Vue 应用无法加载
```bash
# 检查构建输出
ls -la dist-vscode/

# 重新构建
npm run build:vscode
```

### 资源加载失败
- 检查 `extension/src/extension.ts` 中的资源路径配置
- 确保使用了正确的 `webview.asWebviewUri()`

## 📦 打包发布

```bash
# 安装 vsce（如果还没有）
npm install -g vsce

# 打包扩展
cd extension
vsce package
```

## 🎯 下一步

- 扩展已经可以正常运行
- 可以开始开发新功能
- 建议先熟悉 VSCode 扩展 API 和 Webview API
