#!/bin/bash

# 新氧画廊 VSCode 扩展启动脚本
# 自动构建并启动 VSCode 扩展开发环境

echo "🚀 启动新氧画廊 VSCode 扩展开发环境..."

# 检查是否在正确的目录
if [ ! -f "package.json" ] || [ ! -d "extension" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 1. 构建 Vue 应用 (VSCode 版本)
echo "📦 构建 Vue 应用..."
node build-manual.js
if [ $? -ne 0 ]; then
    echo "❌ Vue 应用构建失败"
    exit 1
fi

# 2. 编译扩展 TypeScript 代码
echo "🔨 编译扩展代码..."
npm run extension:compile
if [ $? -ne 0 ]; then
    echo "❌ 扩展编译失败"
    exit 1
fi

# 3. 验证构建结果
echo "✅ 验证构建结果..."
if [ ! -f "extension/out/extension.js" ]; then
    echo "❌ 扩展入口文件不存在"
    exit 1
fi

# 检查是否有 JavaScript 文件
js_files=$(ls dist/index*.js 2>/dev/null | wc -l)
if [ $js_files -eq 0 ]; then
    echo "⚠️  未找到 Vue 应用 JavaScript 文件，请检查 dist 目录"
fi

echo "🎉 构建完成！"
echo ""
echo "📋 接下来的步骤:"
echo "1. 在 VSCode 中打开此项目"
echo "2. 按 F5 键或选择 '运行 VSCode 扩展' 调试配置"
echo "3. 在新的扩展开发宿主窗口中:"
echo "   - 按 Cmd+Shift+P (macOS) 或 Ctrl+Shift+P (Windows/Linux)"
echo "   - 输入 '打开新氧画廊' 命令"
echo "   - 或者在资源管理器中右键选择 '在新氧画廊中打开'"
echo ""
echo "🔧 开发模式:"
echo "- 运行 'npm run vscode:dev' 启动并发开发模式"
echo "- 运行 'npm run extension:watch' 监视扩展代码变化"
echo "- 运行 'npm run dev:vscode' 启动 Vue 应用开发服务器"
echo ""
echo "📝 注意事项:"
echo "- VSCode 扩展不支持热重载，修改扩展代码后需要重新启动扩展宿主窗口"
echo "- Vue 应用支持热重载，修改前端代码后会自动更新"
echo "- 如果遇到问题，请检查 VSCode 开发者控制台的错误信息"
