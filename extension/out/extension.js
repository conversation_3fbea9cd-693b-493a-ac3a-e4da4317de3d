"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
function activate(context) {
    // 注册打开画廊的命令
    const disposable = vscode.commands.registerCommand('sy-gallery.open', () => {
        SYGalleryPanel.createOrShow(context.extensionUri);
    });
    context.subscriptions.push(disposable);
    // 注册webview面板序列化器，用于恢复webview状态
    if (vscode.window.registerWebviewPanelSerializer) {
        vscode.window.registerWebviewPanelSerializer(SYGalleryPanel.viewType, {
            async deserializeWebviewPanel(webviewPanel, state) {
                SYGalleryPanel.revive(webviewPanel, context.extensionUri);
            }
        });
    }
}
exports.activate = activate;
function deactivate() { }
exports.deactivate = deactivate;
/**
 * 管理SY Gallery webview面板
 */
class SYGalleryPanel {
    static createOrShow(extensionUri) {
        console.log('🎯 Creating or showing SY Gallery panel');
        console.log('Extension URI:', extensionUri.toString());
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        // 如果已经有一个面板存在，显示它
        if (SYGalleryPanel.currentPanel) {
            console.log('📋 Revealing existing panel');
            SYGalleryPanel.currentPanel._panel.reveal(column);
            return;
        }
        console.log('🆕 Creating new panel');
        // 否则，创建新面板
        const panel = vscode.window.createWebviewPanel(SYGalleryPanel.viewType, '新氧画廊', column || vscode.ViewColumn.One, {
            // 启用JavaScript
            enableScripts: true,
            // 保持webview内容状态
            retainContextWhenHidden: true,
            // 限制webview只能访问特定目录
            localResourceRoots: [
                vscode.Uri.joinPath(extensionUri, '..', 'dist'),
                vscode.Uri.joinPath(extensionUri, '..', 'public')
            ]
        });
        console.log('📋 Panel created with options:', {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.joinPath(extensionUri, '..', 'dist').toString(),
                vscode.Uri.joinPath(extensionUri, '..', 'public').toString()
            ]
        });
        SYGalleryPanel.currentPanel = new SYGalleryPanel(panel, extensionUri);
    }
    static revive(panel, extensionUri) {
        SYGalleryPanel.currentPanel = new SYGalleryPanel(panel, extensionUri);
    }
    constructor(panel, extensionUri) {
        this._disposables = [];
        console.log('🏗️ Constructing SYGalleryPanel');
        this._panel = panel;
        this._extensionUri = extensionUri;
        // 设置webview的HTML内容
        console.log('📄 Setting webview HTML content');
        this._update();
        // 监听面板关闭事件
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
        // 监听webview消息
        this._panel.webview.onDidReceiveMessage(message => {
            switch (message.command) {
                case 'alert':
                    vscode.window.showInformationMessage(message.text);
                    break;
                case 'error':
                    vscode.window.showErrorMessage(message.text);
                    break;
            }
        }, null, this._disposables);
    }
    _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }
    _getHtmlForWebview(webview) {
        // 构建资源文件的URI
        const galleryDistPath = vscode.Uri.joinPath(this._extensionUri, '..', 'dist');
        const publicPath = vscode.Uri.joinPath(this._extensionUri, '..', 'public');
        // 获取构建后的CSS和JS文件
        let stylesUri = '';
        let scriptUri = '';
        try {
            const distPath = path.join(this._extensionUri.fsPath, '..', 'dist');
            if (fs.existsSync(distPath)) {
                const files = fs.readdirSync(distPath);
                const cssFile = files.find(file => file.startsWith('index') && file.endsWith('.css'));
                const jsFile = files.find(file => file.startsWith('index') && file.endsWith('.js'));
                if (cssFile) {
                    stylesUri = webview.asWebviewUri(vscode.Uri.joinPath(galleryDistPath, cssFile)).toString();
                }
                if (jsFile) {
                    scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(galleryDistPath, jsFile)).toString();
                }
            }
        }
        catch (error) {
            console.error('Error reading dist files:', error);
        }
        // 获取公共资源URI
        const commonCssUri = webview.asWebviewUri(vscode.Uri.joinPath(publicPath, 'common.css'));
        const faviconUri = webview.asWebviewUri(vscode.Uri.joinPath(publicPath, 'favicon.ico'));
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新氧画廊</title>
    <link rel="icon" href="${faviconUri}" />
    <link rel="stylesheet" href="//at.alicdn.com/t/c/font_2598030_cltf7hf8v8e.css" />
    <link rel="stylesheet" href="${commonCssUri}" />
    ${stylesUri ? `<link rel="stylesheet" href="${stylesUri}" />` : ''}
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
        }
        #app {
            width: 100%;
            height: 100vh;
        }
        /* VSCode主题适配 */
        .vscode-dark {
            --primary-color: #007acc;
        }
        .vscode-light {
            --primary-color: #0066cc;
        }
    </style>
</head>
<body class="vscode-body">
    <div id="app">
        ${!scriptUri ? `
        <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; font-size: 16px; padding: 20px;">
            <h2 style="color: #ff6b6b; margin-bottom: 20px;">⚠️ 新氧画廊加载失败</h2>
            <div style="background: var(--vscode-textBlockQuote-background); padding: 15px; border-radius: 8px; max-width: 600px;">
                <p><strong>问题：</strong>未找到构建后的JavaScript文件</p>
                <p><strong>可能原因：</strong></p>
                <ul style="text-align: left;">
                    <li>项目尚未构建，请运行: <code>npm run build:vscode</code></li>
                    <li>构建失败，请检查构建日志</li>
                    <li>dist目录不存在或为空</li>
                </ul>
                <p><strong>调试信息：</strong></p>
                <p>扩展路径: ${this._extensionUri.toString()}</p>
                <p>查找目录: dist</p>
            </div>
        </div>
        ` : ''}
    </div>
    
    <script>
        // VSCode API
        const vscode = acquireVsCodeApi();
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            vscode.postMessage({
                command: 'error',
                text: '发生错误: ' + event.error.message
            });
        });
        
        // 为Vue应用提供VSCode环境检测
        window.__VSCODE_ENV__ = true;
        window.__VSCODE_API__ = vscode;
    </script>
    
    ${scriptUri ? `<script type="module" src="${scriptUri}"></script>` : ''}
</body>
</html>`;
    }
    dispose() {
        SYGalleryPanel.currentPanel = undefined;
        // 清理资源
        this._panel.dispose();
        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
}
SYGalleryPanel.viewType = 'syGallery';
//# sourceMappingURL=extension.js.map