#!/usr/bin/env node

/**
 * 测试文件检测逻辑
 * 模拟扩展中的文件查找过程
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 测试文件检测逻辑...\n');

// 模拟扩展的文件查找逻辑
function testFileDetection() {
    const distPath = path.join(process.cwd(), 'dist-vscode');
    
    console.log(`📁 检查目录: ${distPath}`);
    console.log(`📁 目录存在: ${fs.existsSync(distPath)}`);
    
    if (!fs.existsSync(distPath)) {
        console.log('❌ dist-vscode 目录不存在');
        return false;
    }
    
    try {
        const files = fs.readdirSync(distPath);
        console.log(`📄 目录中的文件数量: ${files.length}`);
        
        // 查找 CSS 和 JS 文件
        const cssFiles = files.filter(file => file.startsWith('index') && file.endsWith('.css'));
        const jsFiles = files.filter(file => file.startsWith('index') && file.endsWith('.js'));
        
        console.log('\n🎨 CSS 文件:');
        cssFiles.forEach(file => console.log(`  ✅ ${file}`));
        
        console.log('\n📜 JavaScript 文件:');
        jsFiles.forEach(file => console.log(`  ✅ ${file}`));
        
        if (cssFiles.length === 0) {
            console.log('⚠️  未找到 CSS 文件');
        }
        
        if (jsFiles.length === 0) {
            console.log('❌ 未找到 JavaScript 文件');
            return false;
        }
        
        // 显示将要使用的文件
        const selectedCss = cssFiles[0];
        const selectedJs = jsFiles[0];
        
        console.log('\n🎯 将使用的文件:');
        console.log(`  CSS: ${selectedCss || '无'}`);
        console.log(`  JS:  ${selectedJs || '无'}`);
        
        return true;
        
    } catch (error) {
        console.error('❌ 读取目录时出错:', error.message);
        return false;
    }
}

// 运行测试
const success = testFileDetection();

console.log('\n📊 测试结果:');
if (success) {
    console.log('🎉 文件检测成功！扩展应该能够正常加载。');
} else {
    console.log('❌ 文件检测失败！需要重新构建项目。');
    console.log('\n🔧 修复步骤:');
    console.log('1. 运行: npm run build:vscode');
    console.log('2. 重新编译扩展: npm run extension:compile');
    console.log('3. 重新测试: node test-file-detection.js');
}
