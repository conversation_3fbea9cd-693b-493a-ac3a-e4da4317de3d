import { build } from 'vite';
import { fileURLToPath } from 'node:url';
import path from 'node:path';

async function buildVSCode() {
  console.log('🚀 开始手动构建 VSCode 版本...');
  
  try {
    // 删除旧的构建目录
    const fs = await import('fs');
    const distPath = path.resolve(process.cwd(), 'dist');
    
    if (fs.existsSync(distPath)) {
      console.log('🗑️  删除旧的 dist 目录...');
      fs.rmSync(distPath, { recursive: true, force: true });
    }
    
    // 使用 vite 配置文件构建
    console.log('📦 开始 Vite 构建...');
    
    const result = await build({
      configFile: 'vite.config.vscode.ts',
      mode: 'production'
    });
    
    console.log('✅ 构建成功！');
    console.log('📁 检查构建结果...');
    
    if (fs.existsSync(distPath)) {
      const files = fs.readdirSync(distPath);
      console.log('构建文件：');
      files.forEach(file => console.log(`  - ${file}`));
    } else {
      console.log('❌ dist 目录不存在');
    }
    
  } catch (error) {
    console.error('❌ 构建失败：', error);
    process.exit(1);
  }
}

buildVSCode();
